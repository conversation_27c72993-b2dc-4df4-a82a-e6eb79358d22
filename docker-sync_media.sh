#!/bin/bash

# Define variables
MC_PATH="$HOME/MinIOClient/mc"
MC_DIR="$HOME/MinIOClient"
REMOTE_ALIAS="remote"
REMOTE_ALIAS_EXISTS=false
REMOTE_ENDPOINT="https://mw-miniost.inkclub.com:9000"
ACCESS_KEY="<ACCESS_KEY>"
SECRET_KEY="<SECRET_KEY>"
REMOTE_PATH="${REMOTE_ALIAS}/ppn-magento-pub-st/media/"
LOCAL_PATH="pub/media"
LOG_PATH="var/log/minio_sync_progress.log"

# Documentation:
# This script automates the setup and synchronization of MinIO client.
# If you need to set up the MinIO client manually, follow these steps:
# 1. Download the MinIO client:
#    wget https://dl.min.io/client/mc/release/linux-amd64/mc -O ~/MinIOClient/mc
# 2. Make the client executable:
#    chmod +x ~/MinIOClient/mc
# 3. Add an alias for your MinIO server:
#    ~/MinIOClient/mc alias set remote https://mw-miniost.inkclub.com:9000 <ACCESS_KEY> <SECRET_KEY> --api S3v4
# 4. Verify the alias is set correctly:
#    ~/MinIOClient/mc alias ls
# 5. Synchronize directories manually:
#    ~/MinIOClient/mc mirror --overwrite --skip-errors --summary --insecure remote/ppn-magento-pub-st/media/ pub/media

# Check if MinIO client exists and is executable
if [ ! -x "$MC_PATH" ]; then
  echo "MinIO client not found or not executable. Downloading it now..."
  mkdir -p "$MC_DIR"
  wget https://dl.min.io/client/mc/release/linux-amd64/mc -O "$MC_PATH"
  chmod +x "$MC_PATH"
  echo "MinIO client downloaded and installed to $MC_DIR."
fi

# Check if the alias 'remote' exists
if "$MC_PATH" ls "$REMOTE_ALIAS" >/dev/null 2>&1; then
  REMOTE_ALIAS_EXISTS=true
else
  REMOTE_ALIAS_EXISTS=false
fi

if [ "$REMOTE_ALIAS_EXISTS" = false ]; then
  echo "Alias '$REMOTE_ALIAS' not found. Set it up with the following command:"
  echo "$MC_PATH alias set $REMOTE_ALIAS $REMOTE_ENDPOINT $ACCESS_KEY $SECRET_KEY --api S3v4"
  exit 1
fi

# Run the mirror command with progress
if [ ! -d "/var/log" ]; then
  echo "/var/log directory does not exist. Creating it..."
  mkdir -p /var/log
fi

echo "Starting synchronization with progress..."
$MC_PATH mirror --overwrite --skip-errors --insecure "$REMOTE_PATH" "$LOCAL_PATH" | tee "$LOG_PATH"

if [ ${PIPESTATUS[0]} -eq 0 ]; then
  echo "Synchronization completed successfully."
else
  echo "Synchronization encountered errors. Check $LOG_PATH for details."
fi

stages:
  - build
  - deploy
#  - sonar

build_artifact:
  stage: build
  before_script:
    - echo "This job build artifacts."
  script:
    - tar -zcvf deploy.tar.gz *
  after_script:
    - echo "Build completed."
  artifacts:
    paths:
      - deploy.tar.gz
  only:
    - deploy/prod
    - deploy/stage01
    - deploy/test01

#sonar_scan:
#  stage: sonar
#  before_script:
#    - echo "This job performs Sonar scanning."
#  script:
#    - /bin/sh cicd/scripts/sonar.sh
#  after_script:
#    - echo "Sonar scanning completed."
#  only:
#    - develop

.deployment_template: &deploy_configuration
  stage: deploy
  dependencies:
    - build_artifact
  script:
    - /bin/sh cicd/scripts/validate.sh
    - /bin/sh cicd/scripts/set_ssh.sh
    - /bin/sh cicd/scripts/deploy.sh
  after_script:
# Enable or Disable Sonar script
#    - /bin/sh cicd/scripts/sonar.sh
    - /bin/sh cicd/scripts/post_deployment.sh

.test_template: &test_configuration
  <<: *deploy_configuration
  environment:
    name: testing
    url: $TEST_ENV_URL
  only:
    - deploy/test01

.stage_template: &stage_configuration
  <<: *deploy_configuration
  environment:
    name: staging
    url: $STAGE_ENV_URL
  only:
    - deploy/stage01

.production_template: &production_configuration
  <<: *deploy_configuration
  environment:
    name: production
    url: $PROD_ENV_URL
  only:
    - deploy/prod

deploy_test_node_1:
  <<: *test_configuration
  before_script:
    - export SSH_HOSTNAME=$TEST1_HOSTNAME
    - export SSH_USERNAME=$TEST1_USERNAME
    - export MAGENTO_ROOT_DIR=$TEST1_MAGENTO_ROOT_DIR
    - export REDIS_HOSTNAME=$TEST_REDIS_HOSTNAME
    - export REDIS_USERNAME=$TEST_REDIS_USERNAME
    - export PHP_VERSION=$TEST_PHP_VERSION

deploy_test_node_2:
  <<: *test_configuration
  before_script:
    - export SSH_HOSTNAME=$TEST2_HOSTNAME
    - export SSH_USERNAME=$TEST2_USERNAME
    - export MAGENTO_ROOT_DIR=$TEST2_MAGENTO_ROOT_DIR
    - export REDIS_HOSTNAME=$TEST_REDIS_HOSTNAME
    - export REDIS_USERNAME=$TEST_REDIS_USERNAME
    - export PHP_VERSION=$TEST_PHP_VERSION

deploy_stage_node_1:
  <<: *stage_configuration
  before_script:
    - echo "Before script. Hostname:$STAGE1_HOSTNAME Username:$STAGE1_USERNAME"
    - export SSH_HOSTNAME=$STAGE1_HOSTNAME
    - export SSH_USERNAME=$STAGE1_USERNAME
    - export MAGENTO_ROOT_DIR=$STAGE1_MAGENTO_ROOT_DIR
    - export REDIS_HOSTNAME=$STAGE_REDIS_HOSTNAME
    - export REDIS_USERNAME=$STAGE_REDIS_USERNAME
    - export PHP_VERSION=$STAGE_PHP_VERSION

deploy_stage_node_2:
  <<: *stage_configuration
  before_script:
    - echo "Before script. Hostname:$STAGE2_HOSTNAME Username:$STAGE2_USERNAME"
    - export SSH_HOSTNAME=$STAGE2_HOSTNAME
    - export SSH_USERNAME=$STAGE2_USERNAME
    - export MAGENTO_ROOT_DIR=$STAGE2_MAGENTO_ROOT_DIR
    - export REDIS_HOSTNAME=$STAGE_REDIS_HOSTNAME
    - export REDIS_USERNAME=$STAGE_REDIS_USERNAME
    - export PHP_VERSION=$STAGE_PHP_VERSION

deploy_prod_node_1:
  <<: *production_configuration
  before_script:
    - export SSH_HOSTNAME=$PROD1_HOSTNAME
    - export SSH_USERNAME=$PROD1_USERNAME
    - export MAGENTO_ROOT_DIR=$PROD1_MAGENTO_ROOT_DIR
    - export REDIS_HOSTNAME=$PROD_REDIS_HOSTNAME
    - export REDIS_USERNAME=$PROD_REDIS_USERNAME
    - export PHP_VERSION=PROD_PHP_VERSION

deploy_prod_node_2:
  <<: *production_configuration
  before_script:
    - export SSH_HOSTNAME=$PROD2_HOSTNAME
    - export SSH_USERNAME=$PROD2_USERNAME
    - export MAGENTO_ROOT_DIR=$PROD2_MAGENTO_ROOT_DIR
    - export REDIS_HOSTNAME=$PROD_REDIS_HOSTNAME
    - export REDIS_USERNAME=$PROD_REDIS_USERNAME
    - export PHP_VERSION=PROD_PHP_VERSION

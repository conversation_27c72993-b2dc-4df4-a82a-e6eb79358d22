#!/bin/bash

# Variables
LOCAL_BACKUP_DIR="$(dirname "$(realpath "$0")")/var/backups"
DOCKER_CONTAINER="ppnlocal-db-1"
MYSQL_USER="root"
MYSQL_PASSWORD="magento2"
MYSQL_DATABASE="magento2"
BACKUP_DATE=$(date +"%Y%m%d_%H%M%S")
BACKUP_FILE="${LOCAL_BACKUP_DIR}/db_backup_${BACKUP_DATE}.sql"

# Ensure backup directory exists
mkdir -p "${LOCAL_BACKUP_DIR}"

# Check if Docker container is running
if ! docker ps --format '{{.Names}}' | grep -q "^${DOCKER_CONTAINER}\$"; then
    echo "Error: Docker container '${DOCKER_CONTAINER}' is not running."
    exit 1
fi

# Perform the backup using `mysqldump`
echo "Starting backup of database '${MYSQL_DATABASE}' from container '${DOCKER_CONTAINER}'..."
docker exec "${DOCKER_CONTAINER}" mysqldump -u"${MYSQL_USER}" -p"${MYSQL_PASSWORD}" "${MYSQL_DATABASE}" > "${BACKUP_FILE}"

# Verify if the backup was successful
if [[ $? -eq 0 ]]; then
    echo "Backup completed successfully! File saved to: ${BACKUP_FILE}"
else
    echo "Error: Backup failed!"
    exit 1
fi

# Optional: Clean up old backups (keep the last 7 backups)
echo "Cleaning up old backups..."
find "${LOCAL_BACKUP_DIR}" -name "db_backup_*.sql" -type f -mtime +7 -exec rm -f {} \;

echo "Backup script finished successfully."

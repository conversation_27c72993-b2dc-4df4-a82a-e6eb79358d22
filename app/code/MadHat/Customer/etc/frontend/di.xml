<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\Customer\Model\AccountManagement">
        <plugin name="madhat_customer_save_invoice_email" type="MadHat\Customer\Plugin\Customer\Model\AccountManagementPlugin" sortOrder="10"/>
    </type>
    <type name="Magento\Customer\Api\CustomerRepositoryInterface">
        <plugin name="madhat_customer_save_invoice_email_repository" type="MadHat\Customer\Plugin\Customer\Api\CustomerRepositoryPlugin" sortOrder="10"/>
    </type>
</config>

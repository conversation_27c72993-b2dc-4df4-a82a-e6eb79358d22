<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="customer">
            <group id="madhat_customer" translate="label" type="text" sortOrder="200" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>MadHat Customer Configuration</label>
                <field id="enable_invoice_email" translate="label comment" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Enable Invoice Email Field</label>
                    <comment>Enable the invoice email field in customer registration form</comment>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <config_path>customer/madhat_customer/enable_invoice_email</config_path>
                </field>
            </group>
        </section>
    </system>
</config>

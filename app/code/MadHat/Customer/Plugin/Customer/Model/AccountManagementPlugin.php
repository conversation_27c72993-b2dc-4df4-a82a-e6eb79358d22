<?php
declare(strict_types=1);

namespace MadHat\Customer\Plugin\Customer\Model;

use Magento\Customer\Api\Data\CustomerInterface;
use Magento\Customer\Model\AccountManagement;
use Magento\Framework\App\RequestInterface;
use MadHat\Customer\Helper\Data as CustomerHelper;

class AccountManagementPlugin
{
    /**
     * @var RequestInterface
     */
    private RequestInterface $request;

    /**
     * @var CustomerHelper
     */
    private CustomerHelper $customerHelper;

    /**
     * @param RequestInterface $request
     * @param CustomerHelper $customerHelper
     */
    public function __construct(
        RequestInterface $request,
        CustomerHelper $customerHelper
    ) {
        $this->request = $request;
        $this->customerHelper = $customerHelper;
    }

    /**
     * Save invoice email during customer creation
     *
     * @param AccountManagement $subject
     * @param CustomerInterface $result
     * @param CustomerInterface $customer
     * @param string $password
     * @param string $redirectUrl
     * @return CustomerInterface
     */
    public function afterCreateAccount(
        AccountManagement $subject,
        CustomerInterface $result,
        CustomerInterface $customer,
        string $password = null,
        string $redirectUrl = ''
    ): CustomerInterface {
        if (!$this->customerHelper->isInvoiceEmailEnabled()) {
            return $result;
        }

        $invoiceEmail = $this->request->getParam('invoice_email');
        if ($invoiceEmail) {
            $result->setCustomAttribute('invoice_email', $invoiceEmail);
            // The customer will be saved by Magento after this plugin
        }

        return $result;
    }
}

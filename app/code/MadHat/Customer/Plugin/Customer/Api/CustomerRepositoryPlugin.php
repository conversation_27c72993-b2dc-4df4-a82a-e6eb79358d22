<?php
declare(strict_types=1);

namespace MadHat\Customer\Plugin\Customer\Api;

use <PERSON>gent<PERSON>\Customer\Api\CustomerRepositoryInterface;
use Magento\Customer\Api\Data\CustomerInterface;
use Magento\Framework\App\RequestInterface;
use MadHat\Customer\Helper\Data as CustomerHelper;

class CustomerRepositoryPlugin
{
    /**
     * @var RequestInterface
     */
    private RequestInterface $request;

    /**
     * @var CustomerHelper
     */
    private CustomerHelper $customerHelper;

    /**
     * @param RequestInterface $request
     * @param CustomerHelper $customerHelper
     */
    public function __construct(
        RequestInterface $request,
        CustomerHelper $customerHelper
    ) {
        $this->request = $request;
        $this->customerHelper = $customerHelper;
    }

    /**
     * Save invoice email during customer save operations
     *
     * @param CustomerRepositoryInterface $subject
     * @param CustomerInterface $customer
     * @param string|null $passwordHash
     * @return array
     */
    public function beforeSave(
        CustomerRepositoryInterface $subject,
        CustomerInterface $customer,
        string $passwordHash = null
    ): array {
        if (!$this->customerHelper->isInvoiceEmailEnabled()) {
            return [$customer, $passwordHash];
        }

        $invoiceEmail = $this->request->getParam('invoice_email');
        if ($invoiceEmail !== null) {
            $customer->setCustomAttribute('invoice_email', $invoiceEmail);
        }

        return [$customer, $passwordHash];
    }
}

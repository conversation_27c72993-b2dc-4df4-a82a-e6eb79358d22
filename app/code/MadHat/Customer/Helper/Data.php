<?php
declare(strict_types=1);

namespace MadHat\Customer\Helper;

use Magento\Framework\App\Helper\AbstractHelper;
use Magento\Framework\App\Helper\Context;
use Magento\Store\Model\ScopeInterface;

class Data extends AbstractHelper
{
    const XML_PATH_ENABLE_INVOICE_EMAIL = 'customer/madhat_customer/enable_invoice_email';

    /**
     * @param Context $context
     */
    public function __construct(Context $context)
    {
        parent::__construct($context);
    }

    /**
     * Check if invoice email field is enabled
     *
     * @param int|null $storeId
     * @return bool
     */
    public function isInvoiceEmailEnabled(?int $storeId = null): bool
    {
        $result = $this->scopeConfig->isSetFlag(
            self::XML_PATH_ENABLE_INVOICE_EMAIL,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );

        // Debug: Log the configuration check
        error_log("Invoice Email Config Check - Store ID: " . ($storeId ?? 'null') . ", Result: " . ($result ? 'true' : 'false'));

        return $result;
    }
}

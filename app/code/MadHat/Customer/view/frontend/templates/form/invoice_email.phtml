<?php
/**
 * @var \MadHat\Customer\Block\Form\InvoiceEmail $block
 */
?>
<!-- DEBUG: Invoice Email Template Loaded -->
<?php if ($block->isInvoiceEmailEnabled()): ?>
<!-- DEBUG: Invoice Email is ENABLED -->
<div class="field field-reserved col-span-2">
    <label for="invoice_email" class="label text-cgrey-90">
        <span><?= $block->escapeHtml(__('Invoice Email')) ?></span>
    </label>
    <div class="control">
        <input type="email"
               name="invoice_email"
               id="invoice_email"
               value="<?= $block->escapeHtmlAttr($block->getInvoiceEmailValue()) ?>"
               title="<?= $block->escapeHtmlAttr(__('Invoice Email')) ?>"
               class="form-input w-full"
               autocomplete="email"
               data-validate="{'validate-email':true}"
               @input.debounce="onChange">
    </div>
</div>
<?php else: ?>
<!-- DEBUG: Invoice Email is DISABLED -->
<?php endif; ?>

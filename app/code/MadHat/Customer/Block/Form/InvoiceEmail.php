<?php
declare(strict_types=1);

namespace MadHat\Customer\Block\Form;

use Magento\Customer\Model\Session;
use Magento\Framework\View\Element\Template;
use Magento\Framework\View\Element\Template\Context;
use MadHat\Customer\Helper\Data as CustomerHelper;

class InvoiceEmail extends Template
{
    /**
     * @var CustomerHelper
     */
    private CustomerHelper $customerHelper;

    /**
     * @var Session
     */
    private Session $customerSession;

    /**
     * @param Context $context
     * @param CustomerHelper $customerHelper
     * @param Session $customerSession
     * @param array $data
     */
    public function __construct(
        Context $context,
        CustomerHelper $customerHelper,
        Session $customerSession,
        array $data = []
    ) {
        $this->customerHelper = $customerHelper;
        $this->customerSession = $customerSession;
        parent::__construct($context, $data);
    }

    /**
     * Check if invoice email field should be displayed
     *
     * @return bool
     */
    public function isInvoiceEmailEnabled(): bool
    {
        return $this->customerHelper->isInvoiceEmailEnabled();
    }

    /**
     * Get invoice email value from form data or customer attribute
     *
     * @return string
     */
    public function getInvoiceEmailValue(): string
    {
        // First check if there's form data (for validation errors)
        $formData = $this->getRequest()->getPost();
        if (isset($formData['invoice_email'])) {
            return $formData['invoice_email'];
        }

        // For edit form, get value from customer attribute
        if ($this->customerSession->isLoggedIn()) {
            $customer = $this->customerSession->getCustomer();
            $invoiceEmailAttribute = $customer->getCustomAttribute('invoice_email');
            if ($invoiceEmailAttribute) {
                return $invoiceEmailAttribute->getValue();
            }
        }

        return '';
    }
}

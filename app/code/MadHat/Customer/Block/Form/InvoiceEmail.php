<?php
declare(strict_types=1);

namespace MadHat\Customer\Block\Form;

use Magento\Framework\View\Element\Template;
use Magento\Framework\View\Element\Template\Context;
use MadHat\Customer\Helper\Data as CustomerHelper;

class InvoiceEmail extends Template
{
    /**
     * @var CustomerHelper
     */
    private CustomerHelper $customerHelper;

    /**
     * @param Context $context
     * @param CustomerHelper $customerHelper
     * @param array $data
     */
    public function __construct(
        Context $context,
        CustomerHelper $customerHelper,
        array $data = []
    ) {
        $this->customerHelper = $customerHelper;
        parent::__construct($context, $data);
    }

    /**
     * Check if invoice email field should be displayed
     *
     * @return bool
     */
    public function isInvoiceEmailEnabled(): bool
    {
        return $this->customerHelper->isInvoiceEmailEnabled();
    }

    /**
     * Get invoice email value from form data
     *
     * @return string
     */
    public function getInvoiceEmailValue(): string
    {
        $formData = $this->getRequest()->getPost();
        return $formData['invoice_email'] ?? '';
    }
}

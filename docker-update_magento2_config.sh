#!/bin/bash

# Variables
DOCKER_DB_CONTAINER="ppnlocal-db-1"
DOCKER_PHP_CONTAINER="ppnlocal-fpm-1"
SQL_FILE="docker-update_magento2_config.sql"
MYSQL_USER="root"
MYSQL_PASSWORD="magento2"
MYSQL_DATABASE="magento2"

ADMIN_USER="codercv"
ADMIN_PASSWORD="1366613nox"
ADMIN_EMAIL="<EMAIL>"
ADMIN_FIRSTNAME="Oleh"
ADMIN_LASTNAME="Hyrbu"

# Ensure the SQL file exists
if [ ! -f "$SQL_FILE" ]; then
    echo "Error: SQL file '$SQL_FILE' not found."
    exit 1
fi

# Check if the DB Docker container is running
if ! docker ps --format '{{.Names}}' | grep -q "^$DOCKER_DB_CONTAINER$"; then
    echo "Error: Docker container '$DOCKER_DB_CONTAINER' is not running."
    exit 2
fi

# Check if the PHP-FPM container is running
if ! docker ps --format '{{.Names}}' | grep -q "^$DOCKER_PHP_CONTAINER$"; then
    echo "Error: Docker container '$DOCKER_PHP_CONTAINER' is not running."
    exit 3
fi

# Execute the SQL file inside the Docker DB container
echo "Executing SQL commands from '$SQL_FILE' on database '$MYSQL_DATABASE' in container '$DOCKER_DB_CONTAINER'..."
chmod 644 "$SQL_FILE"
cat "$SQL_FILE" | docker exec -i "$DOCKER_DB_CONTAINER" mysql -u"$MYSQL_USER" -p"$MYSQL_PASSWORD" "$MYSQL_DATABASE"

if [ $? -eq 0 ]; then
    echo "✅ SQL commands executed successfully. Magento 2 configuration updated."
else
    echo "❌ Error: Failed to execute SQL commands."
    exit 4
fi

# Create Magento 2 Admin User inside PHP-FPM container
echo "Creating Magento 2 Admin User inside '$DOCKER_PHP_CONTAINER'..."

docker exec -i "$DOCKER_PHP_CONTAINER" php bin/magento admin:user:create \
    --admin-user="$ADMIN_USER" \
    --admin-password="$ADMIN_PASSWORD" \
    --admin-email="$ADMIN_EMAIL" \
    --admin-firstname="$ADMIN_FIRSTNAME" \
    --admin-lastname="$ADMIN_LASTNAME"

if [ $? -eq 0 ]; then
    echo "✅ Admin user '$ADMIN_USER' created successfully."
else
    echo "❌ Error: Failed to create admin user."
    exit 5
fi

# Run setup and compilation commands
echo "Running Magento setup, DI compile, and reindex..."

docker exec -i "$DOCKER_PHP_CONTAINER" bash -c "php bin/magento setup:upgrade && php bin/magento setup:di:compile && php bin/magento indexer:reset && php bin/magento indexer:reindex"

if [ $? -eq 0 ]; then
    echo "✅ Magento setup, compile, and reindex completed successfully."
else
    echo "❌ Error: Failed during setup or reindex process."
    exit 6
fi

echo "🎉 All Magento 2 tasks completed!"

exit 0

#!/bin/bash

# Variables
REMOTE_USER="olegh"
REMOTE_HOST="*************"
REMOTE_BACKUP_DIR="~/backups"
LOCAL_BACKUP_DIR="$(dirname "$(realpath "$0")")/var/backups"

# Ensure the local backup directory exists
mkdir -p "$LOCAL_BACKUP_DIR"

# Fetch the list of files from the remote server
echo "Fetching list of backup files from $REMOTE_USER@$REMOTE_HOST..."
FILE_LIST=$(ssh "$REMOTE_USER@$REMOTE_HOST" "ls -1 $REMOTE_BACKUP_DIR 2>/dev/null")

if [ -z "$FILE_LIST" ]; then
    echo "No backup files found in $REMOTE_BACKUP_DIR on $REMOTE_HOST."
    exit 1
fi

# Display available files
echo "Available backup files:"
echo "$FILE_LIST"

# Ask the user to choose a file
read -p "Enter the name of the file to download: " SELECTED_FILE

# Verify the file exists remotely
if ! echo "$FILE_LIST" | grep -q "^$SELECTED_FILE$"; then
    echo "Error: File '$SELECTED_FILE' not found on the remote server."
    exit 2
fi

# Download the selected file
echo "Downloading '$SELECTED_FILE' from $REMOTE_HOST..."
scp "$REMOTE_USER@$REMOTE_HOST:$REMOTE_BACKUP_DIR/$SELECTED_FILE" "$LOCAL_BACKUP_DIR"

# Verify download success
if [ $? -eq 0 ]; then
    echo "File '$SELECTED_FILE' successfully downloaded to $LOCAL_BACKUP_DIR."
else
    echo "Error: Failed to download '$SELECTED_FILE'."
    exit 3
fi

exit 0

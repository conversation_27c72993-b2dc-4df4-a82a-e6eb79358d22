# Base URL update to local env
UPDATE core_config_data
SET value='https://ppn.project/'
WHERE path = 'web/unsecure/base_url';
UPDATE core_config_data
SET value='https://ppn.project/'
WHERE path = 'web/secure/base_url';
# Testing local e-mails settings (https://mailtrap.io/inboxes/608552/messages):
INSERT INTO core_config_data (scope, scope_id, path, value)
VALUES ('default', 0, 'smtp/module/subscribe', '1'),
       ('default', 0, 'smtp/module/create', '1'),
       ('default', 0, 'smtp/module/name', 'l9zgr453csqg8496ie31wmydm082x9cph5jbokcg'),
       ('default', 0, 'smtp/module/email', '<EMAIL>'),
       ('default', 0, 'smtp/module/product_key', 'SMTP-IA6AS7ZFIYUH9UBXBQFG'),
       ('default', 0, 'smtp/module/active', '1'),
       ('default', 0, 'free/module/subscribe', '1'),
       ('default', 0, 'free/module/create', '1'),
       ('default', 0, 'free/module/name', 'l9zgr453csqg8496ie31wmydm082x9cph5jbokcg'),
       ('default', 0, 'free/module/email', '<EMAIL>');
# Switch cache to files
UPDATE core_config_data SET value = '1' WHERE path = 'system/full_page_cache/caching_application';


# Local execution:
#sonar-scanner -Dproject.settings=./sonar.properties
#sonar-scanner -Dproject.settings=/Users/<USER>/source/ppn-magento/sonar-project.properties

sonar.projectKey=PPN-Magento
sonar.projectName=PPN - Magento
sonar.projectVersion=1.3.0
sonar.sourceEncoding=UTF-8

sonar.host.url=https://sonar.inkclub.com/
sonar.login=sqp_1d64903177b50932a702515bfae95acd93cbfbb8

# === Code Quality Rules and Profiling ===
# Activate PHP-specific rules
sonar.language=php

# Increase maximum file length if Magento modules are large
sonar.inclusions=**/*.php
sonar.php.file.suffixes=php,phtml

# Enabling duplication detection for larger projects
sonar.cpd.exclusions=vendor/**,generated/**

# === Source directory for PHP ===
# App/code-only config
#sonar.sources=app/code
#sonar.projectBaseDir=app/code

# Broad scanning, including platform/core
sonar.sources=.

# Exclude Magento core and vendor
sonar.exclusions=vendor/**,generated/**,var/**,pub/**,dev/**,app/code/Magento/**,app/exampleCode/**
#sonar.exclusions=vendor/**,generated/**,var/**,pub/**,app/code/Magento/**,app/code/Magezon/**,app/code/Amasty/**

sonar.php.exclusions=vendor/**,generated/**,dev/**

# === Tests and Coverage ===
# Directory containing your unit and integration tests
sonar.tests=dev/tests/unit,dev/tests/integration

# Only consider PHP test files
sonar.test.inclusions=**/*Test.php

# PHPUnit Coverage Report
# You must generate this before scanning (see section below)
sonar.php.coverage.reportPaths=coverage/coverage.xml

# === Performance Tweaks ===
# Set higher memory for scanner if project is big
sonar.scanner.memory=4096m

# Multithreading: Increase worker threads (default is single thread)
sonar.cpd.workers=2

# === Optional: Branch / Tag Info (for Community, only the main branch matters) ===
# sonar.branch.name=main

# === Logging ===
# Set to DEBUG for troubleshooting
# sonar.verbose=true

#You must generate a code coverage report before the scan:
# Inside your Magento project root
#php vendor/bin/phpunit --coverage-clover=coverage/coverage.xml
Due to incomplete initial config for network in docker-compose.yml, the network created during init clashes with the networks used at iPiccolo.

This procedure logs the current network and assists to re-create the network using other config.

******************************************************************************
From terminal:
'docker network list' - list all docker networks.

from output:
5192a6b4ad92   ppn-local_default                                           bridge    local
3035c04af86b   ppn-local_magento                                           bridge    local
d280db43cd9c   ppn-local_magento-build                                     bridge    local

Now we will inspect 3 docker networks

******************************************************************************
'docker network inspect ppn-local_default'

[
    {
        "Name": "ppn-local_default",
        "Id": "5192a6b4ad9236ccc7008f6a32e989536c6dc8695ce0f32dc8bb67c0e06b56d5",
        "Created": "2023-12-06T10:56:29.7426873Z",
        "Scope": "local",
        "Driver": "bridge",
        "EnableIPv6": false,
        "IPAM": {
            "Driver": "default",
            "Options": null,
            "Config": [
                {
                    "Subnet": "**********/16",
                    "Gateway": "**********"
                }
            ]
        },
        "Internal": false,
        "Attachable": false,
        "Ingress": false,
        "ConfigFrom": {
            "Network": ""
        },
        "ConfigOnly": false,
        "Containers": {},
        "Options": {},
        "Labels": {
            "com.docker.compose.network": "default",
            "com.docker.compose.project": "ppn-local",
            "com.docker.compose.version": "2.23.0"
        }
    }
]

******************************************************************************
'docker network inspect ppn-local_magento'

[
    {
        "Name": "ppn-local_magento",
        "Id": "3035c04af86bd1f7dab9babc163c2a941737762cea627558f5ff205b4a81d36b",
        "Created": "2023-12-06T10:56:29.719966716Z",
        "Scope": "local",
        "Driver": "bridge",
        "EnableIPv6": false,
        "IPAM": {
            "Driver": "default",
            "Options": null,
            "Config": [
                {
                    "Subnet": "**********/16",
                    "Gateway": "**********"
                }
            ]
        },
        "Internal": false,
        "Attachable": false,
        "Ingress": false,
        "ConfigFrom": {
            "Network": ""
        },
        "ConfigOnly": false,
        "Containers": {
            "2ec74adce12095b0de080dc53eef34cfe912a19d7604e9dd6bef82ed73be58fb": {
                "Name": "ppn-local-redis-1",
                "EndpointID": "11a2cec15add574c9578d9ad68fcd1976c50d6992402e43ed7748924fe0d9e30",
                "MacAddress": "02:42:ac:17:00:05",
                "IPv4Address": "**********/16",
                "IPv6Address": ""
            },
            "34e3ed3300c2bbd7bfa26f5675ab06e22c38b0433cfde8e40d672895439569e0": {
                "Name": "ppn-local-rabbitmq-1",
                "EndpointID": "4401e1d924f810ae9762eb8b93ab23f4ec5614137c15a28962b1554fb02cc50b",
                "MacAddress": "02:42:ac:17:00:04",
                "IPv4Address": "**********/16",
                "IPv6Address": ""
            },
            "45ecd84bf668cdf003c908517359e3235a4e5ad48fc11841c46461b4e7fd511e": {
                "Name": "ppn-local-varnish-1",
                "EndpointID": "0764c2fdfa50f7992e9e0e812fc1e809074b5ba94731a9f7354d62e2876a5d62",
                "MacAddress": "02:42:ac:17:00:0a",
                "IPv4Address": "**********0/16",
                "IPv6Address": ""
            },
            "61b8ef808840c852bc3f0b60820dd39d6064d8f5291dcb1d1d6ccfa6a81e806c": {
                "Name": "ppn-local-tls-1",
                "EndpointID": "3a726c4a28d76df3ad0e93e0f9a5e247ee8b45732c1e4731a4274f7cf6767b1c",
                "MacAddress": "02:42:ac:17:00:0b",
                "IPv4Address": "**********1/16",
                "IPv6Address": ""
            },
            "675c94a8a615a68b40f1e5194568d06d3298e04de4bdc2a59b90971e20888370": {
                "Name": "ppn-local-fpm-1",
                "EndpointID": "63eb3917371b118b091bd19123bb22730ca7e4c2c75bd2043fa17754fd4e77b3",
                "MacAddress": "02:42:ac:17:00:07",
                "IPv4Address": "**********/16",
                "IPv6Address": ""
            },
            "b8e4aa86c783376f5b1b667301646e06d887dc7a892c7a01b1b4447fb292ab54": {
                "Name": "ppn-local-elasticsearch-1",
                "EndpointID": "fcc68cfdf534cce4339ba01fd623db929b4696c08044347ac77dc8b77d39f7bc",
                "MacAddress": "02:42:ac:17:00:02",
                "IPv4Address": "**********/16",
                "IPv6Address": ""
            },
            "cb7d476859acef5e93399c977c359f26c5733c2c43936988a8ce3ac19752e61f": {
                "Name": "ppn-local-mailhog-1",
                "EndpointID": "1dfd58a74ffb4d2536844f518d2647e8ce3644c1c28dae09a493f51674e5ebec",
                "MacAddress": "02:42:ac:17:00:03",
                "IPv4Address": "**********/16",
                "IPv6Address": ""
            },
            "d9962cc6957c978950bc0629ceaa6bb72764d5bf65015383df2ff0e9eeec5176": {
                "Name": "ppn-local-web-1",
                "EndpointID": "76c1018fdac771a0c41cb93b52ca9281de9ec54c6d1c79327f676a233625565c",
                "MacAddress": "02:42:ac:17:00:09",
                "IPv4Address": "**********/16",
                "IPv6Address": ""
            },
            "fa9bbe0ce41c949761901a5c319d8a6613bdfd316de31231fe597bb7d6d62ffd": {
                "Name": "ppn-local-db-1",
                "EndpointID": "774e103882d15ddf87713c1679ab44876a1f2877c310eef287491921b72ddcbe",
                "MacAddress": "02:42:ac:17:00:06",
                "IPv4Address": "**********/16",
                "IPv6Address": ""
            }
        },
        "Options": {},
        "Labels": {
            "com.docker.compose.network": "magento",
            "com.docker.compose.project": "ppn-local",
            "com.docker.compose.version": "2.23.0"
        }
    }
]

******************************************************************************

'docker network inspect ppn-local_magento-build'

[
    {
        "Name": "ppn-local_magento-build",
        "Id": "d280db43cd9cbb7d237a7e6e4286875a17da05089c5caa0dc739c5e4b607d68a",
        "Created": "2023-12-06T10:56:29.76149055Z",
        "Scope": "local",
        "Driver": "bridge",
        "EnableIPv6": false,
        "IPAM": {
            "Driver": "default",
            "Options": null,
            "Config": [
                {
                    "Subnet": "**********/16",
                    "Gateway": "**********"
                }
            ]
        },
        "Internal": false,
        "Attachable": false,
        "Ingress": false,
        "ConfigFrom": {
            "Network": ""
        },
        "ConfigOnly": false,
        "Containers": {},
        "Options": {},
        "Labels": {
            "com.docker.compose.network": "magento-build",
            "com.docker.compose.project": "ppn-local",
            "com.docker.compose.version": "2.23.0"
        }
    }
]

******************************************************************************

How to resolve the networking issue:

1. Create 'daemon.json' file with the following contents:

{
	"live-restore": true,
	"bip": "**********/24",
	"default-address-pools": [{
		"base": "**********/16",
		"size": 24
	}]
}

sudo systemctl status docker.service docker.socket
sudo systemctl stop docker.service docker.socket

live-restore — this parameter helps reduce container downtime when the system is shut down or rebooted.
bip — IP address of Docker's bridge-interface in the format "address/network prefix". For example, to use the IP address ********* in a *********/16 network, specify "*********/16".
base — the range of IP addresses for creating Docker networks in the format "network/network prefix". E.g., "10.0.0.0/8".
size — prefix of networks being created.

2. Stop docker
3. Copy file to /etc/docker/daemon.json
4 Start docker

5. Remove incorrect networks using this command:

docker network rm ppn-local_default ppn-local_magento ppn-local_magento-build

6. Create new networks (if you did not setup docker-compose.yml). Otherwise just run "docker-compose up"

docker network create \
  --driver=bridge \
  --ipam-driver=default \
  --subnet=**********/16 \
  --gateway=********** \
  --label=com.docker.compose.network=default \
  --label=com.docker.compose.project=ppn-local \
  --label=com.docker.compose.version=2.23.0 \
  ppn-local_default

docker network create \
  --driver=bridge \
  --ipam-driver=default \
  --subnet=**********/16 \
  --gateway=********** \
  --label=com.docker.compose.network=magento \
  --label=com.docker.compose.project=ppn-local \
  --label=com.docker.compose.version=2.23.0 \
  ppn-local_magento

docker network create \
  --driver=bridge \
  --ipam-driver=default \
  --subnet=**********/16 \
  --gateway=********** \
  --label=com.docker.compose.network=magento-build \
  --label=com.docker.compose.project=ppn-local \
  --label=com.docker.compose.version=2.23.0 \
  ppn-local_magento-build

7. Execute this command to recreate/fix your containers (it will attach the newly created networks to the existing containers):

docker-compose up --force-recreate -d

******************************************************************************

!! This one fails to create a YML network as there is an internal dependency to "default" network. If I try to specify it in yml it fails to start the existing containers. It might work in a completely clean setup (when you have no containers created yet).

  default:
    driver: bridge
    ipam:
      driver: default
      config:
        - subnet: **********/16
          gateway: **********
    labels:
      com.docker.compose.network: default
      com.docker.compose.project: ppn-local
      com.docker.compose.version: 2.23.0

Update your yml file with network specifications if needed:

networks:
  magento:
    driver: bridge
    ipam:
      driver: default
      config:
        - subnet: **********/16
          gateway: **********
    labels:
      com.docker.compose.network: magento
      com.docker.compose.project: ppn-local
      com.docker.compose.version: 2.23.0
  magento-build:
    driver: bridge
    ipam:
      driver: default
      config:
        - subnet: **********/16
          gateway: **********
    labels:
      com.docker.compose.network: magento-build
      com.docker.compose.project: ppn-local
      com.docker.compose.version: 2.23.0
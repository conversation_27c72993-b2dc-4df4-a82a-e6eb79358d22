#!/bin/bash

# Variables
LOCAL_BACKUP_DIR="$(dirname "$(realpath "$0")")/var/backups"
DOCKER_CONTAINER="ppnlocal-db-1"
MYSQL_USER="root"
MYSQL_PASSWORD="magento2"
MYSQL_DATABASE="magento2"

# Ensure the backup directory exists
if [ ! -d "$LOCAL_BACKUP_DIR" ]; then
    echo "Error: Backup directory $LOCAL_BACKUP_DIR does not exist."
    exit 1
fi

# List available backup files
echo "Available backup files in $LOCAL_BACKUP_DIR:"
BACKUP_FILES=$(ls "$LOCAL_BACKUP_DIR" | grep -E '\.sql$|\.zip$|\.bz2$')

if [ -z "$BACKUP_FILES" ]; then
    echo "No backup files found."
    exit 1
fi

echo "$BACKUP_FILES"

# Ask user which file to restore
read -p "Enter the name of the backup file to restore: " SELECTED_FILE

# Verify the file exists
if [ ! -f "$LOCAL_BACKUP_DIR/$SELECTED_FILE" ]; then
    echo "Error: File '$SELECTED_FILE' not found in $LOCAL_BACKUP_DIR."
    exit 2
fi

# Prepare the file for restoration
RESTORE_FILE="$LOCAL_BACKUP_DIR/$SELECTED_FILE"

# Recreate database (delete and create again)
# echo "Recreating database..."
# docker exec -i "$DOCKER_CONTAINER" mysql -u"$MYSQL_USER" -p"$MYSQL_PASSWORD" --force "$MYSQL_DATABASE" -e "DROP DATABASE IF EXISTS magento2;"
# docker exec -i "$DOCKER_CONTAINER" mysql -u"$MYSQL_USER" -p"$MYSQL_PASSWORD" --force "$MYSQL_DATABASE" -e "CREATE DATABASE magento2;"
# echo "Database recreated successfully."

# Handle different file types
case "$SELECTED_FILE" in
    *.sql)
        echo "Restoring SQL file..."
        docker exec -i "$DOCKER_CONTAINER" mysql -u"$MYSQL_USER" -p"$MYSQL_PASSWORD" --force "$MYSQL_DATABASE" < "$RESTORE_FILE"
        ;;
    *.bz2)
        echo "Decompressing and restoring bz2 file..."
        bzip2 -dc "$RESTORE_FILE" | docker exec -i "$DOCKER_CONTAINER" mysql -u"$MYSQL_USER" -p"$MYSQL_PASSWORD" --force "$MYSQL_DATABASE"
        ;;
    *.zip)
        echo "Unzipping and restoring zip file..."
        UNZIPPED_FILE=$(mktemp)
        unzip -p "$RESTORE_FILE" > "$UNZIPPED_FILE"
        docker exec -i "$DOCKER_CONTAINER" mysql -u"$MYSQL_USER" -p"$MYSQL_PASSWORD" --force "$MYSQL_DATABASE" < "$UNZIPPED_FILE"
        rm -f "$UNZIPPED_FILE"
        ;;
    *)
        echo "Error: Unsupported file type '$SELECTED_FILE'."
        exit 3
        ;;
esac

# Check if the restoration was successful
if [ $? -eq 0 ]; then
    echo "Backup '$SELECTED_FILE' successfully restored to the database in container '$DOCKER_CONTAINER'."
else
    echo "Error: Failed to restore the backup."
    exit 4
fi

exit 0

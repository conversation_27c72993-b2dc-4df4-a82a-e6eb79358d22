{"name": "magento2", "author": "Magento Commerce Inc.", "description": "Magento2 node modules dependencies for local development", "license": "(OSL-3.0 OR AFL-3.0)", "repository": {"type": "git", "url": "https://github.com/magento/magento2.git"}, "homepage": "https://magento.com/", "devDependencies": {"glob": "~8.0.1", "grunt": "~1.5.2", "grunt-banner": "~0.6.0", "grunt-continue": "~0.1.0", "grunt-contrib-clean": "~2.0.0", "grunt-contrib-connect": "~3.0.0", "grunt-contrib-cssmin": "~4.0.0", "grunt-contrib-imagemin": "~4.0.0", "grunt-contrib-jasmine": "~4.0.0", "grunt-contrib-less": "~2.1.0", "grunt-contrib-watch": "~1.1.0", "grunt-eslint": "~24.0.0", "grunt-exec": "~3.0.0", "grunt-replace": "~2.0.2", "grunt-styledocco": "~0.3.0", "grunt-template-jasmine-requirejs": "~0.2.3", "grunt-text-replace": "~0.4.0", "imagemin-svgo": "~9.0.0", "less": "3.13.1", "load-grunt-config": "~4.0.1", "morgan": "~1.10.0", "node-minify": "~3.6.0", "path": "~0.12.7", "serve-static": "~1.15.0", "squirejs": "~0.2.1", "strip-json-comments": "~3.1.1", "time-grunt": "~2.0.0", "underscore": "1.13.3"}}